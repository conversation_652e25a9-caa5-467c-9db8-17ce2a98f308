# Mail Auto: Multi-Tenant Email Automation System

> **Status:** Production-ready with Azure Key Vault integration and enterprise security features.  
> **Features:** Multi-format file processing, AI-powered document classification, secure credential management, automated OneDrive organization, and customer self-service onboarding.
>
> **Latest Updates:**
> * ✅ Azure Key Vault integration for secure credential storage
> * ✅ Multi-tenant customer onboarding with OAuth consent flow
> * ✅ Environment-based configuration (development/production)
> * ✅ Managed Identity support for production deployment
> * ✅ Comprehensive migration tools and documentation

---

## 🎯 What It Does

Mail Auto is an enterprise-grade email automation system that processes incoming emails with attachments, automatically classifies documents using AI, and organizes them in OneDrive with intelligent folder structures.

### **Core Pipeline (Per Tenant)**

1. **🔐 Secure Authentication**: Connects to Microsoft 365 via Graph API using Azure Key Vault stored credentials
2. **📧 Email Processing**: Scans inbox for unread emails with attachments  
3. **📄 Multi-Format Analysis**: Extracts text from various file types using specialized processors
4. **🤖 AI Classification**: Uses OpenAI GPT to analyze content and classify document types
5. **📁 Smart Organization**: Routes documents to appropriate OneDrive folders based on configurable rules
6. **📬 Notifications**: Sends optional email notifications for important document types
7. **✅ Completion**: Marks emails as read and logs processing results

### **Supported File Types**
- **PDFs**: Text extraction + OCR fallback for scanned documents
- **Microsoft Office**: Word (.docx), Excel (.xlsx), PowerPoint (.pptx)  
- **Images**: JPG, PNG, GIF, BMP, TIFF with OCR text extraction
- **Plain Text**: TXT, CSV files with direct content reading

### **AI-Powered Features**
- **Document Classification**: Automatically identifies invoices, certificates, contracts, etc.
- **Content Summarization**: Generates human-readable summaries
- **Data Extraction**: Pulls key information (supplier, amount, dates, etc.)
- **Context Awareness**: Analyzes email body text alongside attachments for better accuracy

---

## 🏗️ System Architecture

### **Project Structure**
```
Mail_Auto/
├── core/                           # Core system modules
│   ├── config.py                   # Environment-based configuration management
│   ├── key_vault_service.py        # Azure Key Vault integration
│   ├── tenant_loader.py            # Tenant discovery and credential loading
│   ├── tenant_onboarding.py        # Customer self-service onboarding
│   ├── mail_reader.py              # Microsoft Graph email processing
│   ├── unified_file_analyzer.py    # Main file analysis orchestrator
│   ├── router.py                   # Document routing and folder resolution
│   ├── upload_onedrive.py          # OneDrive upload operations
│   ├── notification.py             # Email notification system
│   ├── file_processors/            # Multi-format file processors
│   │   ├── factory.py              # Processor factory pattern
│   │   ├── base.py                 # Base processor interface
│   │   ├── pdf_processor.py        # PDF text extraction + OCR
│   │   ├── docx_processor.py       # Word document processor
│   │   ├── xlsx_processor.py       # Excel spreadsheet processor
│   │   ├── pptx_processor.py       # PowerPoint processor
│   │   ├── image_processor.py      # Image OCR processor
│   │   └── text_processor.py       # Plain text processor
│   └── interpreter/                # AI analysis module
│       └── chatgpt_api.py          # OpenAI GPT integration
├── tenants/                        # Tenant-specific configurations
│   └── prototype/                  # Development tenant example
│       └── config.json             # Document processing rules
├── main.py                         # Main pipeline orchestrator
├── setup_development.py            # Development environment setup
├── migrate_to_keyvault.py          # Migration utility for Key Vault
├── test_multi_format.py            # Multi-format testing script
├── requirements.txt                # Python dependencies
├── .env                           # Environment configuration (create from template)
├── AZURE_SETUP.md                 # Azure AD and production setup guide
└── KEYVAULT_SETUP.md              # Key Vault configuration guide
```

---

## 🚀 Quick Start

### **Prerequisites**
- Python 3.10+ 
- Azure subscription with Key Vault access
- Microsoft 365 tenant with admin permissions
- OpenAI API key
- Tesseract OCR (for image processing)

### **1. Environment Setup**
```bash
# Clone and navigate to project
git clone <repository-url>
cd Mail_Auto

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your Azure and OpenAI credentials
```

### **2. Azure Configuration**
```bash
# Run automated setup (creates Key Vault, validates dependencies)
python setup_development.py

# Follow the Azure setup guide for detailed configuration
# See: AZURE_SETUP.md and KEYVAULT_SETUP.md
```

### **3. Run the System**
```bash
# Single execution (development testing)
python main.py --once

# Continuous monitoring (production mode)
python main.py
```

### **4. Customer Onboarding**
```bash
# Start onboarding service
python core/tenant_onboarding.py

# Send customers personalized links:
# https://yourdomain.com/onboard/customer-name
```

---

## 📋 Core Components Explained

### **Pipeline Orchestration**
- **`main.py`**: Main entry point that coordinates the entire pipeline
- **`core/unified_file_analyzer.py`**: Orchestrates file processing, AI analysis, and routing

### **Security & Configuration**
- **`core/config.py`**: Environment-based configuration management (dev/prod)
- **`core/key_vault_service.py`**: Secure credential storage and retrieval
- **`core/tenant_loader.py`**: Multi-tenant credential and configuration loading

### **Email & Authentication**
- **`core/mail_reader.py`**: Microsoft Graph API integration for email processing
- **`core/tenant_onboarding.py`**: OAuth consent flow for customer self-service setup

### **File Processing**
- **`core/file_processors/factory.py`**: Determines appropriate processor for each file type
- **`core/file_processors/*.py`**: Specialized processors for different file formats
- **`core/interpreter/chatgpt_api.py`**: AI-powered document analysis and classification

### **Document Management**
- **`core/router.py`**: Intelligent folder routing based on document type and rules
- **`core/upload_onedrive.py`**: OneDrive integration for file organization
- **`core/notification.py`**: Email notifications for important documents

### **Utilities & Migration**
- **`setup_development.py`**: Automated development environment setup
- **`migrate_to_keyvault.py`**: Migration tool for existing JSON-based credentials
- **`test_multi_format.py`**: Testing utility for file processing capabilities

---

## 🔧 Configuration

### **Environment Variables** (`.env` file)
```bash
# Environment
MAIL_AUTO_ENVIRONMENT=development

# Azure Key Vault (Development)
DEV_KEY_VAULT_URL=https://your-dev-kv.vault.azure.net/
DEV_USE_MANAGED_IDENTITY=false
DEV_TENANT_ID=your-azure-tenant-id
DEV_CLIENT_ID=your-service-principal-client-id
DEV_CLIENT_SECRET=your-service-principal-secret

# OpenAI
OPENAI_API_KEY=sk-proj-your-openai-api-key

# Optional
LOG_LEVEL=INFO
DEBUG_MODE=false
```

### **Tenant Configuration** (`tenants/{customer}/config.json`)
```json
{
  "tenant_name": "customer-name",
  "defaults": {
    "storage": {"subfolder_format": "{doc_type}/{yyyy}"},
    "actions": {"upload": true, "notify": false}
  },
  "document_types": {
    "invoice": {
      "keywords": ["invoice", "faktura", "amount due"],
      "storage": {"subfolder_format": "Invoices/{yyyy}/{supplier}"},
      "actions": {"upload": true, "notify": true}
    }
  }
}
```

---

## 📚 Documentation

- **[AZURE_SETUP.md](AZURE_SETUP.md)** - Complete Azure AD setup, production deployment, and customer onboarding
- **[KEYVAULT_SETUP.md](KEYVAULT_SETUP.md)** - Detailed Key Vault configuration and security setup

---

## 🔒 Security Features

- **Azure Key Vault**: Secure credential storage with encryption at rest
- **Managed Identity**: Production deployment without stored secrets
- **Tenant Isolation**: Complete separation of customer data and configurations
- **OAuth Consent**: Customer-controlled permission granting
- **Audit Logging**: Comprehensive tracking of all operations
- **Environment Separation**: Isolated development and production configurations

---

## 💰 Cost Structure

- **Azure Key Vault**: ~$0.018 per customer per month
- **Microsoft Graph API**: Free (with throttling limits)
- **Azure App Service**: $73-146/month (scales to thousands of customers)
- **Total**: ~$0.092 per customer per month at 1,000+ customers

---

## 🎯 Production Deployment

1. **Azure App Service**: Deploy with Managed Identity enabled
2. **Custom Domain**: Configure HTTPS for production OAuth callbacks  
3. **Customer Onboarding**: Send personalized links for self-service setup
4. **Monitoring**: Set up cost alerts and performance monitoring
5. **Scaling**: Horizontal scaling for 5,000+ customers

See [AZURE_SETUP.md](AZURE_SETUP.md) for detailed production deployment instructions.
